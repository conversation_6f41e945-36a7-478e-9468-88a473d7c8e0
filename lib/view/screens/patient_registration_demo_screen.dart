import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/controllers/patient_registration_controller.dart';
import 'package:platix/utils/constants/colors.dart';

class PatientRegistrationDemoScreen extends StatelessWidget {
  const PatientRegistrationDemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final PatientRegistrationController controller = Get.put(PatientRegistrationController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('Patient Registration API Demo'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Obx(() {
        if (controller.isLoading.value && !controller.hasData) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Loading patient registrations...'),
              ],
            ),
          );
        }

        if (controller.hasError.value && !controller.hasData) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text(
                  'Error: ${controller.errorMessage.value}',
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: Colors.red),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: controller.refreshData,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (!controller.hasData) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.people_outline, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'No patient registrations found',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // API Info Card
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.primary.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'API Response Data:',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text('Total Records: ${controller.paginationData.value.total}'),
                  Text('Current Page: ${controller.paginationData.value.page}'),
                  Text('Records per Page: ${controller.paginationData.value.limit}'),
                  Text('Total Pages: ${controller.paginationData.value.totalPages}'),
                  Text('Loaded Records: ${controller.patientRegistrations.length}'),
                ],
              ),
            ),
            
            // Search Field
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: TextField(
                controller: controller.searchController,
                decoration: InputDecoration(
                  hintText: 'Search patients...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: controller.searchQuery.value.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: controller.clearSearch,
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                onChanged: controller.onSearchChanged,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Patient List
            Expanded(
              child: RefreshIndicator(
                onRefresh: controller.refreshData,
                child: ListView.builder(
                  controller: controller.scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: controller.patientRegistrations.length + 
                             (controller.isLoading.value ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index >= controller.patientRegistrations.length) {
                      return const Center(
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: CircularProgressIndicator(),
                        ),
                      );
                    }
                    
                    final patient = controller.patientRegistrations[index];
                    
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: AppColors.primary,
                          child: Text(
                            patient.displayName.isNotEmpty 
                                ? patient.displayName[0].toUpperCase()
                                : 'P',
                            style: const TextStyle(color: Colors.white),
                          ),
                        ),
                        title: Text(
                          patient.displayName,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (patient.email != null)
                              Text('Email: ${patient.email}'),
                            if (patient.mobile != null)
                              Text('Mobile: ${patient.mobile}'),
                            if (patient.gender != null)
                              Text('Gender: ${patient.gender}'),
                            if (patient.patientRegId != null)
                              Text('Patient ID: ${patient.patientRegId}'),
                            Text('Registered: ${patient.formattedDate}'),
                          ],
                        ),
                        isThreeLine: true,
                        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                        onTap: () {
                          // Show patient details
                          Get.dialog(
                            AlertDialog(
                              title: Text(patient.displayName),
                              content: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('First Name: ${patient.firstName ?? 'N/A'}'),
                                  Text('Last Name: ${patient.lastName ?? 'N/A'}'),
                                  Text('Email: ${patient.email ?? 'N/A'}'),
                                  Text('Mobile: ${patient.mobile ?? 'N/A'}'),
                                  Text('Gender: ${patient.gender ?? 'N/A'}'),
                                  Text('Age: ${patient.age ?? 'N/A'}'),
                                  Text('Address: ${patient.address ?? 'N/A'}'),
                                  Text('Patient ID: ${patient.patientRegId ?? 'N/A'}'),
                                  Text('Created: ${patient.createdAt ?? 'N/A'}'),
                                  Text('Updated: ${patient.updatedAt ?? 'N/A'}'),
                                ],
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Get.back(),
                                  child: const Text('Close'),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        );
      }),
    );
  }
}
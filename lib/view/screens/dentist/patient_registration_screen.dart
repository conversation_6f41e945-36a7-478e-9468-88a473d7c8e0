import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/controllers/patient_registration_controller.dart';
import 'package:platix/services/permission_service.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/utils/constants/colors.dart';
import 'package:platix/view/screens/dentist/create_patient_registration_screen.dart';
import 'package:platix/view/widgets/custom_text_form_field.dart';

class PatientRegistrationScreen extends StatelessWidget {
  const PatientRegistrationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final PermissionService permissionService = PermissionService();
    final PatientRegistrationController controller = Get.put(PatientRegistrationController());

    return Visibility(
      visible: permissionService.hasAnyPermission('Registration', ['is_view', 'is_list', 'is_add']),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Patient Registration'),
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () => controller.refreshData(),
            ),
          ],
        ),
        body: Column(
          children: [
            // Search and Filter Section
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Patient Registration List',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  // Search Field
                  CustomTextFormField(
                    hintText: 'Search by name, email, mobile, or patient ID...',
                    controller: controller.searchController,
                    prefix: Container(
                      margin: const EdgeInsets.all(12),
                      child: const Icon(Icons.search),
                    ),
                    suffix: Obx(() => controller.searchQuery.value.isNotEmpty
                        ? Container(
                            margin: const EdgeInsets.all(8),
                            child: IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: controller.clearSearch,
                            ),
                          )
                        : const SizedBox.shrink()),
                    onChanged: controller.onSearchChanged,
                  ),
                  const SizedBox(height: 16),
                  // Stats Row
                  Obx(() => Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        controller.totalRecordsText,
                        style: const TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                      Text(
                        controller.paginationText,
                        style: const TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                    ],
                  )),
                ],
              ),
            ),
            // Data Table Section
            Expanded(
              child: Obx(() {
                if (controller.isLoading.value && !controller.hasData) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (controller.hasError.value && !controller.hasData) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.error_outline, size: 64, color: Colors.red),
                        const SizedBox(height: 16),
                        Text(
                          'Error: ${controller.errorMessage.value}',
                          textAlign: TextAlign.center,
                          style: const TextStyle(color: Colors.red),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: controller.refreshData,
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }

                if (!controller.hasData) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.people_outline, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text(
                          'No patient registrations found',
                          style: TextStyle(fontSize: 16, color: Colors.grey),
                        ),
                      ],
                    ),
                  );
                }

                return Visibility(
                  visible: permissionService.hasPermission('Registration', 'is_list'),
                  child: RefreshIndicator(
                    onRefresh: controller.refreshData,
                    child: SingleChildScrollView(
                      controller: controller.scrollController,
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: DataTable(
                          columnSpacing: 10,
                          horizontalMargin: 10,
                          headingRowColor: WidgetStateProperty.all(AppColors.primary),
                          headingTextStyle: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                          columns: const [
                            DataColumn(label: Text('S.No')),
                            DataColumn(label: Text('Patient Name')),
                            DataColumn(label: Text('Email')),
                            DataColumn(label: Text('Mobile')),
                            DataColumn(label: Text('Gender')),
                            DataColumn(label: Text('Patient ID')),
                            DataColumn(label: Text('Registration Date')),
                            DataColumn(label: Text('Edit')),
                            DataColumn(label: Text('Delete')),
                          ],
                          rows: [
                            ...controller.patientRegistrations.asMap().entries.map((entry) {
                              final index = entry.key;
                              final patient = entry.value;
                              final isEven = index % 2 == 0;
                              
                              return DataRow(
                                color: WidgetStateProperty.all(
                                  isEven ? AppColors.primary2 : Colors.white,
                                ),
                                cells: [
                                  DataCell(Text('${index + 1}')),
                                  DataCell(Text(patient.displayName)),
                                  DataCell(Text(patient.email ?? 'N/A')),
                                  DataCell(Text(patient.mobile ?? 'N/A')),
                                  DataCell(Text(patient.gender ?? 'N/A')),
                                  DataCell(Text(patient.patientRegId ?? 'N/A')),
                                  DataCell(Text(patient.formattedDate)),
                                  DataCell(
                                    Visibility(
                                      visible: permissionService.hasPermission('Registration', 'is_edit'),
                                      child: IconButton(
                                        icon: const Icon(Icons.edit, color: AppColors.primary),
                                        onPressed: () {
                                          Get.to(() => CreatePatientRegistrationScreen(
                                            patientData: patient,
                                          ));
                                        },
                                      ),
                                    ),
                                  ),
                                  DataCell(
                                    Visibility(
                                      visible: permissionService.hasPermission('Registration', 'is_delete'),
                                      child: IconButton(
                                        icon: const Icon(Icons.delete, color: AppColors.primary),
                                        onPressed: () {
                                          Get.defaultDialog(
                                            title: 'Delete Patient',
                                            titleStyle: const TextStyle(
                                              color: AppColors.primary,
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                            ),
                                            middleText: 'Are you sure you want to delete patient ${patient.displayName}?',
                                            middleTextStyle: const TextStyle(
                                              color: AppColors.black,
                                              fontSize: 14,
                                            ),
                                            backgroundColor: AppColors.white,
                                            radius: 12,
                                            textConfirm: 'Delete',
                                            textCancel: 'Cancel',
                                            confirmTextColor: Colors.white,
                                            cancelTextColor: AppColors.primary,
                                            buttonColor: AppColors.primary,
                                            onConfirm: () {
                                              Get.back();
                                              // Call the actual delete API using database ID
                                              controller.deletePatient(
                                                patient.id ?? '',
                                                patient.displayName,
                                              );
                                            },
                                            onCancel: () {
                                              Get.back();
                                            },
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            }).toList(),
                            // Loading indicator row for pagination
                            if (controller.isLoading.value && controller.hasData)
                              DataRow(
                                cells: List.generate(9, (index) => const DataCell(
                                  Center(child: CircularProgressIndicator()),
                                )),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              }),
            ),
            // Load More Button (if needed)
            Obx(() => controller.showLoadMore
                ? Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: ElevatedButton(
                      onPressed: controller.loadNextPage,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Load More'),
                    ),
                  )
                : const SizedBox.shrink()),
          ],
        ),
        floatingActionButton: Visibility(
          visible: permissionService.hasPermission('Registration', 'is_add'),
          child: FloatingActionButton(
            onPressed: () {
              Get.to(() => const CreatePatientRegistrationScreen(
                patientData: null, // Explicitly null for new patient
              ));
            },
            backgroundColor: AppColors.primary,
            child: const Icon(Icons.add, color: Colors.white),
          ),
        ),
      ),
    );
  }
}
